import { DateTime } from 'luxon';

import { StoryItemDto } from '@malou-io/package-dto';
import {
    MediaType,
    PlatformKey,
    PostPublicationStatus,
    PublicationErrorCode,
    RecurrentStoryFrequency,
    RemoveMethodsFromEntity,
} from '@malou-io/package-utils';

import { SocialPostAuthor } from ':modules/posts-v2/social-posts/models/social-post-author';
import { SocialPostMedia } from ':modules/posts-v2/social-posts/models/social-post-media';
import { StoryToDuplicate } from ':modules/stories/v2/models/story-to-duplicate';
import { IUpsertStory } from ':modules/stories/v2/models/upsert-story';
import { DEFAULT_PHOTO_DURATION_IN_SECONDS } from ':modules/stories/v2/models/utils';

export type IStoryItem = RemoveMethodsFromEntity<StoryItem> & { id: string };

export class StoryItem {
    id: string;
    published: PostPublicationStatus;
    isPublishing: boolean;
    bindingId?: string;
    plannedPublicationDate: Date | null;
    platformKeys: PlatformKey[];
    medias: SocialPostMedia[];
    feedbackMessageCount: number;
    author?: SocialPostAuthor;
    socialCreatedAt?: Date;
    sortDate?: Date;
    mostRecentPublicationErrorCode?: PublicationErrorCode;
    socialLink?: string;
    recurrentStoryFrequency: RecurrentStoryFrequency;

    constructor(data: IStoryItem) {
        this.id = data.id;
        this.published = data.published;
        this.isPublishing = data.isPublishing;
        this.bindingId = data.bindingId;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.platformKeys = data.platformKeys;
        this.medias = data.medias;
        this.feedbackMessageCount = data.feedbackMessageCount;
        this.author = data.author;
        this.socialCreatedAt = data.socialCreatedAt;
        this.sortDate = data.sortDate;
        this.mostRecentPublicationErrorCode = data.mostRecentPublicationErrorCode;
        this.socialLink = data.socialLink;
        this.recurrentStoryFrequency = data.recurrentStoryFrequency;
    }

    static fromDto(dto: StoryItemDto): StoryItem {
        return new StoryItem({
            id: dto.id,
            published: dto.published,
            isPublishing: dto.isPublishing,
            bindingId: dto.bindingId,
            plannedPublicationDate: dto.plannedPublicationDate ? new Date(dto.plannedPublicationDate) : null,
            platformKeys: dto.platformKeys,
            medias: dto.medias.map((media) => SocialPostMedia.fromDto(media)),
            feedbackMessageCount: dto.feedbackMessageCount,
            author: dto.author ? SocialPostAuthor.fromDto(dto.author) : undefined,
            socialCreatedAt: dto.socialCreatedAt ? new Date(dto.socialCreatedAt) : undefined,
            sortDate: dto.sortDate ? new Date(dto.sortDate) : undefined,
            mostRecentPublicationErrorCode: dto.mostRecentPublicationErrorCode,
            socialLink: dto.socialLink,
            recurrentStoryFrequency: dto.recurrentStoryFrequency ?? RecurrentStoryFrequency.NONE,
        });
    }

    static fromIUpsertStory(upsertStory: IUpsertStory): StoryItem {
        return new StoryItem({
            id: upsertStory.id,
            published: upsertStory.published,
            isPublishing: upsertStory.isPublishing,
            bindingId: upsertStory.bindingId,
            plannedPublicationDate: upsertStory.plannedPublicationDate,
            platformKeys: upsertStory.platformKeys,
            medias: upsertStory.medias.map((media) => SocialPostMedia.fromEditionMedia(media)),
            feedbackMessageCount: upsertStory.feedbacks?.isOpen ? upsertStory.feedbacks.feedbackMessages.length : 0,
            author: upsertStory.author,
            socialCreatedAt: upsertStory.socialCreatedAt,
            sortDate: upsertStory.socialCreatedAt ?? upsertStory.plannedPublicationDate ?? undefined,
            mostRecentPublicationErrorCode: upsertStory.mostRecentPublicationErrorCode,
            socialLink: upsertStory.socialLink,
            recurrentStoryFrequency: upsertStory.recurrentStoryFrequency ?? RecurrentStoryFrequency.NONE,
        });
    }

    static fromStoryToDuplicate(storyToDuplicate: StoryToDuplicate): StoryItem {
        return new StoryItem({
            id: storyToDuplicate.id,
            published: storyToDuplicate.published,
            isPublishing: false,
            bindingId: storyToDuplicate.bindingId,
            plannedPublicationDate: storyToDuplicate.plannedPublicationDate,
            platformKeys: storyToDuplicate.platformKeys,
            medias: storyToDuplicate.medias,
            feedbackMessageCount: 0,
            author: undefined,
            socialCreatedAt: storyToDuplicate.socialCreatedAt ?? undefined,
            sortDate: storyToDuplicate.socialCreatedAt ?? storyToDuplicate.plannedPublicationDate ?? undefined,
            mostRecentPublicationErrorCode: undefined,
            socialLink: undefined,
            recurrentStoryFrequency: storyToDuplicate.recurrentStoryFrequency ?? RecurrentStoryFrequency.NONE,
        });
    }

    isActive(): boolean {
        if (this.published !== PostPublicationStatus.PUBLISHED || !this.socialCreatedAt) {
            return false;
        }
        const socialCreatedAt = this.socialCreatedAt;
        const oneDayAfterStoryItemCreationDate = DateTime.fromJSDate(socialCreatedAt).plus({ days: 1 }).toJSDate();
        return oneDayAfterStoryItemCreationDate > new Date();
    }

    getPostDate(): Date | undefined {
        const date = this.sortDate ?? this.socialCreatedAt ?? this.plannedPublicationDate;
        return date ? new Date(date) : undefined;
    }

    isCurrentOrFutureStory(): boolean {
        if (this.isActive()) {
            return true;
        }
        const date = this.getPostDate();
        return date ? date > new Date() : false;
    }

    getMediaDuration(index: number): number {
        if (!this.medias[index]) {
            return DEFAULT_PHOTO_DURATION_IN_SECONDS;
        }
        return this.medias[index].type === MediaType.VIDEO ? (this.medias[index].duration ?? 0) : DEFAULT_PHOTO_DURATION_IN_SECONDS;
    }

    getRemainingHours(): number {
        if (!this.isActive() || !this.socialCreatedAt) {
            return 0;
        }
        const socialCreatedAt = DateTime.fromJSDate(this.socialCreatedAt);
        const endActiveStory = socialCreatedAt.plus({ day: 1 });
        const remainingHours = endActiveStory.diff(DateTime.now(), 'hours').toObject().hours;
        return remainingHours ? Math.floor(remainingHours) : 0;
    }

    canEdit = (): boolean => this.published !== PostPublicationStatus.PUBLISHED && !this.isPublishing;

    canOpenSocialLink = (): boolean => this.isActive() && !!this.socialLink;

    canDuplicate = (): boolean => !this.isPublishing || this.published === PostPublicationStatus.PUBLISHED;

    canDelete(): boolean {
        return !this.isPublishing && this.published !== PostPublicationStatus.PUBLISHED;
    }

    copyWith(data: Partial<IStoryItem>): StoryItem {
        return new StoryItem({ ...this, ...data });
    }
}
