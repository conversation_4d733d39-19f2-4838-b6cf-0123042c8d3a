import { ChangeDetectionStrategy, Component, inject, input, output } from '@angular/core';

import { RecurrentStoryFrequency } from '@malou-io/package-utils';

import { UpsertStoryContext } from ':modules/stories/v2/components/upsert-story-modal/contexts/upsert-story.context';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { UpsertGenericPostModalFooterComponent } from ':shared/components/posts-v2/upsert-generic-post-modal-footer/upsert-generic-post-modal-footer.component';

@Component({
    selector: 'app-upsert-story-modal-footer',
    templateUrl: './upsert-story-modal-footer.component.html',
    styleUrls: ['./upsert-story-modal-footer.component.scss'],
    imports: [UpsertGenericPostModalFooterComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UpsertStoryModalFooterComponent {
    readonly isSubmitting = input.required<boolean>();
    readonly isDisabled = input.required<boolean>();
    readonly cancel = output<void>();
    readonly saveStories = output<SubmitPublicationStatus>();

    private readonly _upsertStoryContext = inject(UpsertStoryContext);

    readonly selectedOption = this._upsertStoryContext.submitPublicationStatus;
    readonly postErrors = this._upsertStoryContext.postErrors;

    readonly willDuplicate = this._upsertStoryContext.upsertStoryState.duplicateToOtherRestaurants;
    readonly selectedDate = this._upsertStoryContext.upsertStoryState.upsertStory.plannedPublicationDate;
    readonly recurrentStoryFrequency = this._upsertStoryContext.upsertStoryState.upsertStory.recurrentStoryFrequency;

    onSelectedDateChange(date: Date | null): void {
        this._upsertStoryContext.updatePlannedPublicationDate(date);
    }

    onRecurrentStoryFrequencyChange(value: RecurrentStoryFrequency | undefined): void {
        if (!value) {
            return;
        }
        this._upsertStoryContext.updateRecurrentStoryFrequency(value);
    }
}
