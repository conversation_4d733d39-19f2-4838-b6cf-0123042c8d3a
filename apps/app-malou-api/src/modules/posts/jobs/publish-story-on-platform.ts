import { Job } from 'agenda';
import { omit } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { PostPublicationStatus, RecurrentStoryFrequency, TimeInMilliseconds } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { publishStoryOnPlatformValidator } from ':helpers/validators/jobs/posts-jobs.validators';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PublishStoryOnPlatformUseCase } from ':modules/posts/v2/use-cases/publish-story-on-platform/publish-story-on-platform.use-case';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@singleton()
export class PublishStoryOnPlatformJob extends GenericJobDefinition {
    constructor(
        private readonly _publishStoryOnPlatformUseCase: PublishStoryOnPlatformUseCase,
        private readonly _storiesRepository: StoriesRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {
        super({
            agendaJobName: AgendaJobName.PUBLISH_STORY_ON_PLATFORM,
            lockLifetimeMs: 15 * TimeInMilliseconds.MINUTE,
            getLogMetadata: async (job: Job) => {
                const data = publishStoryOnPlatformValidator.parse(job.attrs.data);
                const { storyId } = data;
                const { restaurantId, author } = await this._storiesRepository.findOneOrFail({
                    filter: { _id: toDbId(storyId) },
                    projection: { restaurantId: 1, author: 1 },
                    options: { lean: true },
                });

                assert(restaurantId, 'Missing restaurantId on post');
                assert(author, 'Missing author on post');

                return {
                    restaurant: { id: restaurantId.toString() },
                    user: { id: author._id.toString() },
                };
            },
        });
    }

    async executeJob(job: Job): Promise<void> {
        const data = publishStoryOnPlatformValidator.parse(job.attrs.data);
        const { storyId } = data;
        await this._publishStoryOnPlatformUseCase.execute(storyId);
    }

    async duplicateStoryOnPlatforms(storyId: string): Promise<void> {
        const story = await this._storiesRepository.findOne({
            filter: { _id: toDbId(storyId) },
            options: { lean: true },
        });
        assert(story, 'Story not found');
        for (const platformKey of story.keys) {
            const platform = await this._platformsRepository.findOne({
                filter: { restaurantId: story.restaurantId, key: platformKey },
                options: { lean: true },
            });
            assert(platform, 'Platform not found');
            await this._storiesRepository.create({
                data: {
                    ...omit(story, ['_id', 'platformId']),
                    key: platformKey,
                    keys: [],
                    platformId: platform._id,
                    malouStoryId: story.malouStoryId,
                    isStory: true,
                    published: PostPublicationStatus.PENDING,
                },
                options: { lean: true },
            });

            // TODO stories-v2 @Cyril finir ce use case
            // const platformPost = await this._storiesRepository.create({
            //     data: {
            //         ...this._mapToPlatformPost(post, platformKey),
            //         _id: newDbId(),
            //         tries: 0,
            //         key: platformKey,
            //         keys: [platformKey],
            //         platformId: platform?._id,
            //         sortDate,
            //         isPublishing: true,
            //     },
            // });
        }

        // TODO stories-v2 @Cyril tu utilises pas encore cette fonction, mais c'est un truc à avoir en tête
        // + relancer une programmation avec la fonction getNextOccurrence qui existe déjà dans utils
        if (!story.recurrentStoryFrequency || story.recurrentStoryFrequency === RecurrentStoryFrequency.NONE) {
            // Delete the original story
            await this._storiesRepository.deleteOne({
                filter: { _id: story._id },
            });
        }
    }
}
