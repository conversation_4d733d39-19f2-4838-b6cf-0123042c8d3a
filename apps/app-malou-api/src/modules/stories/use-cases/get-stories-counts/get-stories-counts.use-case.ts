import { singleton } from 'tsyringe';

import { GetStoriesCountsDto } from '@malou-io/package-dto';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@singleton()
export class GetStoriesCountsUseCase {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute(restaurantId: string): Promise<GetStoriesCountsDto> {
        const storiesCounts = await this._storiesRepository.getStoriesCounts(restaurantId);
        return {
            total: storiesCounts.total ?? 0,
            error: storiesCounts.error ?? 0,
            draft: storiesCounts.draft ?? 0,
            feedbacks: storiesCounts.feedbacks ?? 0,
            recurrent: storiesCounts.recurrent ?? 0,
        };
    }
}
