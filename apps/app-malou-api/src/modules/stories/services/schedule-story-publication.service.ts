import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';

@singleton()
export class ScheduleStoryPublicationService {
    constructor(private readonly _agendaSingleton: AgendaSingleton) {}

    // TODO stories-v2 @Cyril clean userId (ici et dans l'appel) si on ne l'utilise vraiment pas
    async scheduleStoryPublication(userId: string, storyId: string, date: Date): Promise<void> {
        await this.cancelPostPublication(storyId);
        await this._agendaSingleton.schedule(date, AgendaJobName.PUBLISH_STORY_ON_PLATFORM, {
            storyId,
        });

        logger.info('[STORY PUBLICATION] Rescheduled story publication', { storyId, date });
    }

    async schedulePostPublicationV1(restaurantId: string, date: Date, malouStoryId: string, keys: PlatformKey[]): Promise<void> {
        await this._agendaSingleton.schedule(date, AgendaJobName.INITIALIZE_STORY, {
            restaurantId,
            keys,
            malouStoryId,
        });

        logger.info('[STORY PUBLICATION] Scheduled story publication V1', { restaurantId, keys, malouStoryId });
    }

    async cancelPostPublication(storyId: string): Promise<void> {
        const deletedJobsCount = await this._agendaSingleton.deleteJobs({
            name: AgendaJobName.PUBLISH_STORY_ON_PLATFORM,
            'data.storyId': { $in: [storyId, toDbId(storyId)] },
        });

        logger.info('[STORY PUBLICATION] Cancelled story publication', { storyId, deletedJobsCount });
    }
}
