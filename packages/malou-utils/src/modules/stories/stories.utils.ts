import { RecurrentStoryFrequency } from './stories.interface';

export function getNextOccurrence(startDate: Date, frequency: RecurrentStoryFrequency): Date {
    const next = new Date(startDate);

    switch (frequency) {
        case RecurrentStoryFrequency.DAILY:
            next.setDate(next.getDate() + 1);
            break;

        case RecurrentStoryFrequency.WEEKLY:
            next.setDate(next.getDate() + 7);
            break;

        case RecurrentStoryFrequency.WEEKLY_WEEKDAYS:
            // move to next weekday (monday to friday)
            do {
                next.setDate(next.getDate() + 1);
            } while (next.getDay() === 0 || next.getDay() === 6); // 0=sunday, 6=saturday
            break;

        case RecurrentStoryFrequency.WEEKLY_WEEKENDS:
            // move to next weekend (saturday or sunday)
            do {
                next.setDate(next.getDate() + 1);
            } while (next.getDay() !== 0 && next.getDay() !== 6);
            break;

        default:
            throw new Error(`Unknown frequency: ${frequency}`);
    }

    return next;
}

export function getOccurrencesBetween(startDate: Date, endDate: Date, frequency: RecurrentStoryFrequency, initialDate: Date): Date[] {
    const occurrences: Date[] = [];
    const current = new Date(startDate);

    // First check if the start date is a valid occurrence
    if (isValidOccurrence(current, frequency)) {
        occurrences.push(new Date(current));
    }

    while (current < endDate) {
        switch (frequency) {
            case RecurrentStoryFrequency.DAILY:
                current.setDate(current.getDate() + 1);
                break;

            case RecurrentStoryFrequency.WEEKLY:
                current.setDate(current.getDate() + 7);
                break;

            case RecurrentStoryFrequency.WEEKLY_WEEKDAYS:
                do {
                    current.setDate(current.getDate() + 1);
                } while (current.getDay() === 0 || current.getDay() === 6); // skip saturday and sunday
                break;

            case RecurrentStoryFrequency.WEEKLY_WEEKENDS:
                do {
                    current.setDate(current.getDate() + 1);
                } while (current.getDay() !== 0 && current.getDay() !== 6); // skip monday to friday
                break;

            default:
                throw new Error(`Unknown frequency: ${frequency}`);
        }

        if (current <= endDate) {
            occurrences.push(new Date(current));
        }
    }

    return occurrences;
}

function isValidOccurrence(date: Date, frequency: RecurrentStoryFrequency): boolean {
    const day = date.getDay();
    switch (frequency) {
        case RecurrentStoryFrequency.DAILY:
        case RecurrentStoryFrequency.WEEKLY:
            return true;
        case RecurrentStoryFrequency.WEEKLY_WEEKDAYS:
            return day >= 1 && day <= 5;
        case RecurrentStoryFrequency.WEEKLY_WEEKENDS:
            return day === 0 || day === 6;
        default:
            return false;
    }
}
