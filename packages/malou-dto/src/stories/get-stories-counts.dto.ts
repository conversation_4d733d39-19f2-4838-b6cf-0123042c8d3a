import { z } from 'zod';

import { restaurantIdParamsTransformValidator } from '../common';

export interface GetStoriesCountsDto {
    total: number;
    error: number;
    draft: number;
    feedbacks: number;
    recurrent: number;
}

export const getStoriesCountsParamsValidator = restaurantIdParamsTransformValidator;
export type GetStoriesCountsParamsDto = z.infer<typeof getStoriesCountsParamsValidator>;
